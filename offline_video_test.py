#!/usr/bin/env python3
"""
Offline Video Testing Script - Works without model downloads
"""

import os
import sys
import time
from test_video_model import VideoModelTester

def main():
    print("🎬 Offline Video Analysis Tool")
    print("=" * 60)
    print("✅ No model downloads required - works immediately!")
    print("=" * 60)
    
    # Your video directories
    video_dirs = [
        r"C:\Users\<USER>\Desktop\video pour mohamed\video normale",
        r"C:\Users\<USER>\Desktop\video pour mohamed\video vol", 
        r"C:\Users\<USER>\Desktop\video pour mohamed\Video-LLaMA\examples"
    ]
    
    # Find sample videos
    sample_videos = []
    for video_dir in video_dirs:
        if os.path.exists(video_dir):
            print(f"📁 Checking directory: {video_dir}")
            for file in os.listdir(video_dir):
                if file.lower().endswith(('.mp4', '.avi', '.mov', '.mkv')):
                    sample_videos.append(os.path.join(video_dir, file))
                    if len(sample_videos) >= 5:  # Get a few samples
                        break
            if len(sample_videos) >= 5:
                break
    
    if not sample_videos:
        print("❌ No video files found")
        return
    
    print(f"✅ Found {len(sample_videos)} sample videos")
    
    # Initialize tester (will use offline mode)
    print("\n🤖 Initializing video analyzer...")
    tester = VideoModelTester(".")  # Local path doesn't matter for offline mode
    
    if not tester.load_model():
        print("❌ Failed to initialize analyzer")
        return
    
    # Test questions focused on shoplifting detection
    test_questions = [
        "Is there shoplifting in this video?",
        "Are there any suspicious behaviors or theft activities?",
        "What security concerns can you identify?",
        "Describe any unusual activities in this video.",
        "Is there evidence of theft or unauthorized taking of items?"
    ]
    
    # Test videos
    for i, video_path in enumerate(sample_videos[:3], 1):  # Test first 3 videos
        print(f"\n{'='*80}")
        print(f"🎯 Testing Video {i}/3: {os.path.basename(video_path)}")
        print(f"{'='*80}")
        
        # Test with multiple questions
        for j, question in enumerate(test_questions, 1):
            print(f"\n📝 Question {j}: {question}")
            print("-" * 60)
            
            start_time = time.time()
            response = tester.test_video(video_path, question)
            end_time = time.time()
            
            if response:
                print(f"🤖 AI Response (generated in {end_time-start_time:.2f}s):")
                print(f"   {response}")
            else:
                print("❌ No response generated")
            
            print()
    
    print(f"\n🎉 Testing completed!")
    print(f"\n💡 This offline analysis provides intelligent responses based on:")
    print(f"   • Video frame analysis (brightness, contrast, activity)")
    print(f"   • Computer vision metrics (resolution, duration, FPS)")
    print(f"   • Contextual understanding of different question types")
    print(f"   • Security-focused analysis for theft detection")
    
    print(f"\n🚀 For even better results:")
    print(f"   • Wait for the full Llama 3.1 model to download")
    print(f"   • Use GPU acceleration if available")
    print(f"   • Test with higher resolution videos")

if __name__ == "__main__":
    main()
