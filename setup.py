#!/usr/bin/env python3
"""
Setup script for the video model testing environment.
"""

import subprocess
import sys
import os

def run_command(command):
    """Run a command and return success status."""
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✓ {command}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {command}")
        print(f"Error: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("Error: Python 3.8 or higher is required.")
        return False
    print(f"✓ Python {version.major}.{version.minor}.{version.micro}")
    return True

def install_dependencies():
    """Install required dependencies."""
    print("Installing dependencies...")
    
    # Check if pip is available
    if not run_command("pip --version"):
        print("Error: pip is not available.")
        return False
    
    # Install PyTorch (with CUDA support if available)
    print("Installing PyTorch...")
    if not run_command("pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118"):
        print("Falling back to CPU-only PyTorch...")
        if not run_command("pip install torch torchvision torchaudio"):
            return False
    
    # Install other requirements
    print("Installing other requirements...")
    return run_command("pip install -r requirements.txt")

def check_gpu():
    """Check if GPU is available."""
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            print(f"✓ GPU available: {gpu_name} (Count: {gpu_count})")
            return True
        else:
            print("⚠ No GPU detected. Model will run on CPU (slower).")
            return False
    except ImportError:
        print("⚠ PyTorch not installed yet.")
        return False

def create_sample_structure():
    """Create sample directory structure."""
    directories = ["videos", "outputs"]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"✓ Created directory: {directory}")
        else:
            print(f"✓ Directory exists: {directory}")

def main():
    print("Llama 3.1 Video Clip Model - Setup")
    print("=" * 40)
    
    # Check Python version
    if not check_python_version():
        return
    
    # Install dependencies
    if not install_dependencies():
        print("Failed to install dependencies.")
        return
    
    # Check GPU availability
    check_gpu()
    
    # Create directory structure
    create_sample_structure()
    
    print("\n" + "=" * 40)
    print("Setup completed successfully!")
    print("\nNext steps:")
    print("1. Place your video files in the 'videos' directory")
    print("2. Run: python example_usage.py")
    print("3. Or run: python test_video_model.py --video_dir ./videos")
    print("\nFor help: python test_video_model.py --help")

if __name__ == "__main__":
    main()
