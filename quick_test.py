#!/usr/bin/env python3
"""
Quick test script for your video samples
"""

import os
import sys
from pathlib import Path
from test_video_model import VideoModelTester

def main():
    print("🎬 Llama 3.1 Video Testing - Quick Start")
    print("=" * 50)
    
    # Your video directories
    video_dirs = [
        r"C:\Users\<USER>\Desktop\video pour mohamed\video normale",
        r"C:\Users\<USER>\Desktop\video pour mohamed\video vol", 
        r"C:\Users\<USER>\Desktop\video pour mohamed\Video-LLaMA\examples"
    ]
    
    # Find some sample videos
    sample_videos = []
    for video_dir in video_dirs:
        if os.path.exists(video_dir):
            print(f"📁 Checking directory: {video_dir}")
            for file in os.listdir(video_dir):
                if file.lower().endswith(('.mp4', '.avi', '.mov', '.mkv')):
                    sample_videos.append(os.path.join(video_dir, file))
                    if len(sample_videos) >= 3:  # Just get a few samples
                        break
            if len(sample_videos) >= 3:
                break
    
    if not sample_videos:
        print("❌ No video files found in the specified directories")
        return
    
    print(f"✅ Found {len(sample_videos)} sample videos:")
    for i, video in enumerate(sample_videos, 1):
        print(f"  {i}. {os.path.basename(video)}")
    
    # Initialize the model tester
    print("\n🤖 Initializing model...")
    tester = VideoModelTester(model_path=".")
    
    # Load the model
    if not tester.load_model():
        print("❌ Failed to load model")
        return
    
    print("✅ Model loaded successfully!")
    
    # Test questions
    questions = [
        "Describe what you see in this video.",
        "What activities are happening in this video?",
        "What objects can you identify in this video?"
    ]
    
    # Test the first video with different questions
    test_video = sample_videos[0]
    print(f"\n🎯 Testing with video: {os.path.basename(test_video)}")
    
    for i, question in enumerate(questions, 1):
        print(f"\n--- Test {i}/3 ---")
        print(f"Question: {question}")
        
        try:
            result = tester.test_video(test_video, question)
            if result:
                print("✅ Test completed successfully")
            else:
                print("⚠️ Test completed but no response generated")
        except Exception as e:
            print(f"❌ Error during test: {e}")
    
    print(f"\n🎉 Testing completed!")
    print(f"📝 You can test more videos using:")
    print(f"   python test_video_model.py --video \"path/to/video.mp4\"")
    print(f"   python test_video_model.py --video_dir \"C:\\Users\\<USER>\\Desktop\\video pour mohamed\\video normale\"")

if __name__ == "__main__":
    main()
