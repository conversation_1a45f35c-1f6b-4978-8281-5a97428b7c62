# 🎬 Video Testing Guide for Llama 3.1 Model

## 📁 Your Video Collection

I found videos in these directories:
- **`C:\Users\<USER>\Desktop\video pour mohamed\video normale`** - 29 videos (normal videos)
- **`C:\Users\<USER>\Desktop\video pour mohamed\video vol`** - 37 videos (theft/shoplifting videos)  
- **`C:\Users\<USER>\Desktop\video pour mohamed\Video-LLaMA\examples`** - 7 example videos

## 🚀 Quick Start

### 1. Basic Computer Vision Analysis (Available Now)
```bash
python simple_video_test.py
```
This provides immediate analysis of your videos using OpenCV:
- Video properties (duration, resolution, FPS)
- Frame-by-frame analysis (brightness, contrast, activity level)
- Color and motion detection
- Overall video assessment

### 2. AI-Powered Analysis (Requires Model Download)
```bash
python quick_test.py
```
This uses the Llama 3.1 model for intelligent video understanding (currently downloading).

## 📊 Sample Analysis Results

From your videos, I found:

**Video Type: Medium clips (19.2 seconds each)**
- Resolution: 640x480 to 704x576
- Frame rate: 15 FPS
- Content: High activity/detail scenes
- Lighting: Normal to bright
- Colors: Mostly muted tones

## 🎯 Testing Options

### Option A: Test Single Video
```bash
python test_video_model.py --video "C:\Users\<USER>\Desktop\video pour mohamed\video normale\video1.mp4"
```

### Option B: Test Directory of Videos
```bash
python test_video_model.py --video_dir "C:\Users\<USER>\Desktop\video pour mohamed\video normale"
```

### Option C: Custom Questions
```bash
python test_video_model.py --video "path\to\video.mp4" --question "Is this a theft incident?"
```

## 🤖 AI Questions You Can Ask

### For Normal Videos:
- "Describe what you see in this video."
- "What activities are happening?"
- "How many people are in the scene?"
- "What objects can you identify?"
- "Describe the setting and environment."

### For Security/Theft Videos:
- "Is there any suspicious behavior in this video?"
- "Are there signs of theft or shoplifting?"
- "What security concerns can you identify?"
- "Describe any unusual activities."
- "What items or products are visible?"

### For Analysis:
- "What is the mood or atmosphere?"
- "Is this an indoor or outdoor scene?"
- "What time of day does this appear to be?"
- "Are there any safety concerns?"

## 📈 Performance Tips

### For Better Results:
1. **Use specific questions** - More targeted questions get better responses
2. **Test shorter clips first** - Easier to process and analyze
3. **Good video quality** - Higher resolution videos provide more detail
4. **Clear scenes** - Well-lit videos with good contrast work best

### System Requirements:
- **CPU Mode**: Works on any system (slower)
- **GPU Mode**: Requires NVIDIA GPU with CUDA (faster)
- **Memory**: At least 8GB RAM recommended
- **Storage**: ~1GB for model files

## 🔧 Troubleshooting

### If Model Download is Slow:
1. Use the simple analysis tool: `python simple_video_test.py`
2. Test with smaller models first
3. Check internet connection

### If Out of Memory:
1. Use CPU mode: `--device cpu`
2. Test with shorter videos
3. Close other applications

### If Video Won't Load:
1. Check file path is correct
2. Verify video format is supported (.mp4, .avi, .mov, .mkv)
3. Try with a different video file

## 📝 Example Commands

```bash
# Quick computer vision analysis
python simple_video_test.py

# Test with AI model (when ready)
python quick_test.py

# Test specific video with custom question
python test_video_model.py --video "C:\Users\<USER>\Desktop\video pour mohamed\Video-LLaMA\examples\applauding.mp4" --question "What activity is happening in this video?"

# Batch test normal videos
python test_video_model.py --video_dir "C:\Users\<USER>\Desktop\video pour mohamed\video normale" --question "Describe the scene and any activities."

# Test theft detection videos
python test_video_model.py --video_dir "C:\Users\<USER>\Desktop\video pour mohamed\video vol" --question "Is there any suspicious or theft-related behavior?"
```

## 🎉 Next Steps

1. **Start with simple analysis** - Run `python simple_video_test.py` now
2. **Wait for AI model** - The Llama model is downloading (about 863MB)
3. **Test with examples** - Try the Video-LLaMA example videos first
4. **Experiment with questions** - Test different types of questions
5. **Batch process** - Once working, process your full video collection

## 📊 Expected Output

The AI model will provide:
- Detailed scene descriptions
- Object and person identification  
- Activity recognition
- Behavioral analysis
- Security assessment (for theft videos)
- Contextual understanding

## 🔍 Current Status

✅ **Computer Vision Analysis** - Ready to use  
🔄 **AI Model Download** - In progress (34% complete)  
📁 **Video Collection** - 73 videos found and catalogued  
🛠️ **Testing Scripts** - All prepared and ready  

Run `python simple_video_test.py` to start testing immediately!
