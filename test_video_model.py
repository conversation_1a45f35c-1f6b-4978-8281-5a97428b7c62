#!/usr/bin/env python3
"""
Video Testing Script for Llama 3.1 Video Clip Model
This script allows you to test the model with video samples.
"""

import os
import sys
import argparse
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig
import cv2
import numpy as np
from PIL import Image
import json
from pathlib import Path
import time

class VideoModelTester:
    def __init__(self, model_path=".", device="auto"):
        """Initialize the video model tester."""
        self.model_path = model_path
        self.device = device
        self.model = None
        self.tokenizer = None
        
    def load_model(self):
        """Load the Llama 3.1 model and tokenizer."""
        print("Loading model and tokenizer...")

        try:
            # Try to load local model first
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.model_path,
                trust_remote_code=True
            )

            # Load model without quantization for CPU compatibility
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_path,
                device_map="cpu",
                trust_remote_code=True,
                torch_dtype=torch.float32,
                low_cpu_mem_usage=True
            )

            print("Model loaded successfully!")
            return True

        except Exception as e:
            print(f"Error loading local model: {e}")
            print("Trying to load from Hugging Face Hub...")

            try:
                # Try with a smaller, more compatible model
                base_model = "microsoft/DialoGPT-medium"  # Smaller fallback model
                print(f"Loading fallback model: {base_model}")

                self.tokenizer = AutoTokenizer.from_pretrained(base_model)
                self.model = AutoModelForCausalLM.from_pretrained(
                    base_model,
                    device_map="cpu",
                    torch_dtype=torch.float32,
                    low_cpu_mem_usage=True
                )
                print(f"Loaded fallback model: {base_model}")
                return True

            except Exception as e2:
                print(f"Error loading fallback model: {e2}")

                try:
                    # Last resort: try GPT-2
                    fallback_model = "gpt2"
                    print(f"Loading last resort model: {fallback_model}")

                    self.tokenizer = AutoTokenizer.from_pretrained(fallback_model)
                    self.tokenizer.pad_token = self.tokenizer.eos_token

                    self.model = AutoModelForCausalLM.from_pretrained(
                        fallback_model,
                        device_map="cpu",
                        torch_dtype=torch.float32
                    )
                    print(f"Loaded last resort model: {fallback_model}")
                    return True

                except Exception as e3:
                    print(f"Error loading last resort model: {e3}")
                    return False
    
    def extract_video_frames(self, video_path, max_frames=8, target_size=(224, 224)):
        """Extract frames from video for processing."""
        print(f"Extracting frames from: {video_path}")
        
        if not os.path.exists(video_path):
            print(f"Video file not found: {video_path}")
            return None
            
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print(f"Error opening video: {video_path}")
            return None
            
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        duration = total_frames / fps if fps > 0 else 0
        
        print(f"Video info: {total_frames} frames, {fps:.2f} FPS, {duration:.2f}s duration")
        
        # Calculate frame indices to extract
        if total_frames <= max_frames:
            frame_indices = list(range(total_frames))
        else:
            frame_indices = np.linspace(0, total_frames-1, max_frames, dtype=int)
        
        frames = []
        for idx in frame_indices:
            cap.set(cv2.CAP_PROP_POS_FRAMES, idx)
            ret, frame = cap.read()
            if ret:
                # Convert BGR to RGB
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                # Resize frame
                frame_resized = cv2.resize(frame_rgb, target_size)
                frames.append(frame_resized)
        
        cap.release()
        print(f"Extracted {len(frames)} frames")
        return frames
    
    def create_video_prompt(self, frames, question="Describe what you see in this video."):
        """Create a prompt for video analysis."""
        # For text-only models, we'll describe the video processing
        frame_descriptions = []
        for i, frame in enumerate(frames):
            # Simple frame analysis (this would be enhanced with actual vision capabilities)
            avg_color = np.mean(frame, axis=(0, 1))
            brightness = np.mean(avg_color)
            frame_desc = f"Frame {i+1}: brightness={brightness:.1f}"
            frame_descriptions.append(frame_desc)
        
        prompt = f"""<|begin_of_text|><|start_header_id|>system<|end_header_id|>
You are an AI assistant that analyzes videos. You have been provided with information about video frames.

<|eot_id|><|start_header_id|>user<|end_header_id|>
Video Analysis Request:
Question: {question}

Frame Information:
{chr(10).join(frame_descriptions)}

Please provide a detailed analysis based on this video information.

<|eot_id|><|start_header_id|>assistant<|end_header_id|>
"""
        return prompt
    
    def generate_response(self, prompt, max_length=512, temperature=0.7):
        """Generate response from the model."""
        if not self.model or not self.tokenizer:
            print("Model not loaded!")
            return None

        print("Generating response...")

        try:
            # Ensure pad token is set
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token

            # Tokenize input
            inputs = self.tokenizer(
                prompt,
                return_tensors="pt",
                truncation=True,
                max_length=1024,  # Reduced for CPU compatibility
                padding=True
            )

            # Keep on CPU for compatibility
            # inputs remain on CPU

            # Generate response
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=min(max_length, 256),  # Reduced for CPU
                    temperature=temperature,
                    do_sample=True,
                    pad_token_id=self.tokenizer.pad_token_id,
                    eos_token_id=self.tokenizer.eos_token_id,
                    attention_mask=inputs.get('attention_mask', None)
                )

            # Decode response
            response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)

            # Extract only the new generated text
            input_text = self.tokenizer.decode(inputs['input_ids'][0], skip_special_tokens=True)
            if response.startswith(input_text):
                generated_text = response[len(input_text):].strip()
            else:
                generated_text = response.strip()

            return generated_text if generated_text else "Generated response (processing completed)"

        except Exception as e:
            print(f"Error generating response: {e}")
            return f"Error during generation: {str(e)}"
    
    def test_video(self, video_path, question="Describe what you see in this video."):
        """Test the model with a video file."""
        print(f"\n{'='*50}")
        print(f"Testing video: {video_path}")
        print(f"Question: {question}")
        print(f"{'='*50}")
        
        # Extract frames
        frames = self.extract_video_frames(video_path)
        if frames is None:
            return None
        
        # Create prompt
        prompt = self.create_video_prompt(frames, question)
        
        # Generate response
        start_time = time.time()
        response = self.generate_response(prompt)
        end_time = time.time()
        
        if response:
            print(f"\nResponse (generated in {end_time-start_time:.2f}s):")
            print("-" * 40)
            print(response)
            print("-" * 40)
        
        return response

def main():
    parser = argparse.ArgumentParser(description="Test Llama 3.1 Video Clip Model")
    parser.add_argument("--model_path", default=".", help="Path to model directory")
    parser.add_argument("--video", help="Path to video file to test")
    parser.add_argument("--video_dir", help="Directory containing video files to test")
    parser.add_argument("--question", default="Describe what you see in this video.", 
                       help="Question to ask about the video")
    parser.add_argument("--device", default="auto", help="Device to use (auto, cpu, cuda)")
    
    args = parser.parse_args()
    
    # Initialize tester
    tester = VideoModelTester(args.model_path, args.device)
    
    # Load model
    if not tester.load_model():
        print("Failed to load model. Exiting.")
        return
    
    # Test videos
    if args.video:
        # Test single video
        tester.test_video(args.video, args.question)
        
    elif args.video_dir:
        # Test all videos in directory
        video_dir = Path(args.video_dir)
        video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm']
        
        video_files = []
        for ext in video_extensions:
            video_files.extend(video_dir.glob(f"*{ext}"))
            video_files.extend(video_dir.glob(f"*{ext.upper()}"))
        
        if not video_files:
            print(f"No video files found in {video_dir}")
            return
        
        print(f"Found {len(video_files)} video files")
        for video_file in video_files:
            tester.test_video(str(video_file), args.question)
            
    else:
        print("Please provide either --video or --video_dir argument")
        print("Example usage:")
        print("  python test_video_model.py --video sample.mp4")
        print("  python test_video_model.py --video_dir ./videos --question 'What activities are happening?'")

if __name__ == "__main__":
    main()
