#!/usr/bin/env python3
"""
Example usage of the video model tester.
This script demonstrates how to test the model with sample videos.
"""

import os
from test_video_model import VideoModelTester

def main():
    print("Llama 3.1 Video Clip Model - Example Usage")
    print("=" * 50)
    
    # Initialize the tester
    tester = VideoModelTester(model_path=".")
    
    # Load the model
    print("Loading model...")
    if not tester.load_model():
        print("Failed to load model. Please check your setup.")
        return
    
    # Example questions you can ask about videos
    example_questions = [
        "Describe what you see in this video.",
        "What activities are happening in this video?",
        "What objects can you identify in this video?",
        "Describe the setting and environment.",
        "What is the main action or event in this video?",
        "How many people are in this video?",
        "What is the mood or atmosphere of this video?"
    ]
    
    print("\nExample questions you can ask:")
    for i, question in enumerate(example_questions, 1):
        print(f"{i}. {question}")
    
    # Check for sample videos in common locations
    sample_locations = [
        "./videos",
        "./samples", 
        "./test_videos",
        "."
    ]
    
    video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm']
    found_videos = []
    
    for location in sample_locations:
        if os.path.exists(location):
            for file in os.listdir(location):
                if any(file.lower().endswith(ext) for ext in video_extensions):
                    found_videos.append(os.path.join(location, file))
    
    if found_videos:
        print(f"\nFound {len(found_videos)} video files:")
        for video in found_videos:
            print(f"  - {video}")
        
        # Test the first video with a sample question
        print(f"\nTesting with first video: {found_videos[0]}")
        result = tester.test_video(found_videos[0], example_questions[0])
        
    else:
        print("\nNo video files found in common locations.")
        print("To test with your own videos:")
        print("1. Place video files in a 'videos' folder")
        print("2. Run: python test_video_model.py --video path/to/your/video.mp4")
        print("3. Or run: python test_video_model.py --video_dir ./videos")
        
        # Create a sample command
        print("\nExample commands:")
        print("python test_video_model.py --video sample.mp4")
        print("python test_video_model.py --video sample.mp4 --question 'What activities are happening?'")
        print("python test_video_model.py --video_dir ./videos")

if __name__ == "__main__":
    main()
