#!/usr/bin/env python3
"""
Simple video testing script using a lightweight approach
"""

import os
import cv2
import numpy as np
from pathlib import Path
import time

class SimpleVideoAnalyzer:
    def __init__(self):
        """Initialize the simple video analyzer."""
        pass
    
    def extract_video_info(self, video_path):
        """Extract basic information from video."""
        print(f"📹 Analyzing video: {os.path.basename(video_path)}")
        
        if not os.path.exists(video_path):
            print(f"❌ Video file not found: {video_path}")
            return None
            
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print(f"❌ Error opening video: {video_path}")
            return None
            
        # Get video properties
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        duration = total_frames / fps if fps > 0 else 0
        
        # Extract sample frames for analysis
        sample_frames = []
        frame_indices = np.linspace(0, total_frames-1, min(5, total_frames), dtype=int)
        
        for idx in frame_indices:
            cap.set(cv2.CAP_PROP_POS_FRAMES, idx)
            ret, frame = cap.read()
            if ret:
                sample_frames.append(frame)
        
        cap.release()
        
        # Analyze frames
        analysis = self.analyze_frames(sample_frames)
        
        info = {
            'filename': os.path.basename(video_path),
            'duration': duration,
            'fps': fps,
            'resolution': f"{width}x{height}",
            'total_frames': total_frames,
            'sample_frames': len(sample_frames),
            'analysis': analysis
        }
        
        return info
    
    def analyze_frames(self, frames):
        """Analyze the extracted frames."""
        if not frames:
            return "No frames to analyze"
        
        analysis = []
        
        for i, frame in enumerate(frames):
            # Convert to different color spaces for analysis
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
            
            # Basic statistics
            brightness = np.mean(gray)
            contrast = np.std(gray)
            
            # Color analysis
            dominant_hue = np.mean(hsv[:,:,0])
            saturation = np.mean(hsv[:,:,1])
            
            # Motion detection (simple edge detection)
            edges = cv2.Canny(gray, 50, 150)
            edge_density = np.sum(edges > 0) / (edges.shape[0] * edges.shape[1])
            
            frame_analysis = {
                'frame_number': i + 1,
                'brightness': round(brightness, 2),
                'contrast': round(contrast, 2),
                'dominant_hue': round(dominant_hue, 2),
                'saturation': round(saturation, 2),
                'edge_density': round(edge_density, 4),
                'description': self.describe_frame(brightness, contrast, edge_density, saturation)
            }
            
            analysis.append(frame_analysis)
        
        return analysis
    
    def describe_frame(self, brightness, contrast, edge_density, saturation):
        """Generate a simple description based on frame statistics."""
        description = []
        
        # Brightness analysis
        if brightness < 50:
            description.append("dark scene")
        elif brightness > 200:
            description.append("bright scene")
        else:
            description.append("normal lighting")
        
        # Contrast analysis
        if contrast > 60:
            description.append("high contrast")
        elif contrast < 20:
            description.append("low contrast")
        
        # Edge density (activity level)
        if edge_density > 0.1:
            description.append("high activity/detail")
        elif edge_density < 0.05:
            description.append("low activity/simple scene")
        
        # Color saturation
        if saturation > 100:
            description.append("colorful")
        elif saturation < 50:
            description.append("muted colors")
        
        return ", ".join(description) if description else "normal scene"
    
    def generate_video_summary(self, info):
        """Generate a summary of the video."""
        if not info:
            return "Unable to analyze video"
        
        summary = f"""
🎬 Video Analysis Summary
========================
📁 File: {info['filename']}
⏱️  Duration: {info['duration']:.2f} seconds
🎞️  FPS: {info['fps']:.2f}
📐 Resolution: {info['resolution']}
🖼️  Total Frames: {info['total_frames']}

📊 Frame Analysis:
"""
        
        for frame_data in info['analysis']:
            summary += f"\n  Frame {frame_data['frame_number']}: {frame_data['description']}"
            summary += f" (brightness: {frame_data['brightness']}, contrast: {frame_data['contrast']})"
        
        # Overall assessment
        avg_brightness = np.mean([f['brightness'] for f in info['analysis']])
        avg_contrast = np.mean([f['contrast'] for f in info['analysis']])
        avg_edge_density = np.mean([f['edge_density'] for f in info['analysis']])
        
        summary += f"\n\n🔍 Overall Assessment:"
        summary += f"\n  • Average brightness: {avg_brightness:.1f}"
        summary += f"\n  • Average contrast: {avg_contrast:.1f}"
        summary += f"\n  • Activity level: {'High' if avg_edge_density > 0.08 else 'Medium' if avg_edge_density > 0.04 else 'Low'}"
        
        if info['duration'] < 5:
            summary += f"\n  • Video type: Short clip"
        elif info['duration'] < 30:
            summary += f"\n  • Video type: Medium clip"
        else:
            summary += f"\n  • Video type: Long video"
        
        return summary

def main():
    print("🎬 Simple Video Analysis Tool")
    print("=" * 50)
    
    # Your video directories
    video_dirs = [
        r"C:\Users\<USER>\Desktop\video pour mohamed\video normale",
        r"C:\Users\<USER>\Desktop\video pour mohamed\video vol", 
        r"C:\Users\<USER>\Desktop\video pour mohamed\Video-LLaMA\examples"
    ]
    
    # Find sample videos
    sample_videos = []
    for video_dir in video_dirs:
        if os.path.exists(video_dir):
            print(f"📁 Checking directory: {video_dir}")
            for file in os.listdir(video_dir):
                if file.lower().endswith(('.mp4', '.avi', '.mov', '.mkv')):
                    sample_videos.append(os.path.join(video_dir, file))
                    if len(sample_videos) >= 5:  # Get a few samples
                        break
            if len(sample_videos) >= 5:
                break
    
    if not sample_videos:
        print("❌ No video files found")
        return
    
    print(f"✅ Found {len(sample_videos)} sample videos")
    
    # Initialize analyzer
    analyzer = SimpleVideoAnalyzer()
    
    # Analyze videos
    for i, video_path in enumerate(sample_videos[:3], 1):  # Test first 3 videos
        print(f"\n{'='*60}")
        print(f"🎯 Testing Video {i}/3")
        print(f"{'='*60}")
        
        start_time = time.time()
        info = analyzer.extract_video_info(video_path)
        end_time = time.time()
        
        if info:
            summary = analyzer.generate_video_summary(info)
            print(summary)
            print(f"\n⏱️  Analysis completed in {end_time-start_time:.2f} seconds")
        else:
            print("❌ Failed to analyze video")
    
    print(f"\n🎉 Analysis completed!")
    print(f"\n💡 This is a basic computer vision analysis.")
    print(f"   For AI-powered video understanding, the full model needs to download.")

if __name__ == "__main__":
    main()
