# Llama 3.1 Video Clip Model Testing

This repository contains testing scripts for the Llama 3.1 video clip model.

## Quick Start

### 1. Setup Environment

First, run the setup script to install dependencies:

```bash
python setup.py
```

This will:
- Check Python version compatibility
- Install PyTorch and other dependencies
- Check for GPU availability
- Create necessary directories

### 2. Prepare Video Samples

Place your video files in the `videos` directory. Supported formats:
- MP4, AVI, MOV, MKV, WMV, FLV, WebM

### 3. Test the Model

#### Option A: Quick Test
```bash
python example_usage.py
```

#### Option B: Test Single Video
```bash
python test_video_model.py --video path/to/your/video.mp4
```

#### Option C: Test Multiple Videos
```bash
python test_video_model.py --video_dir ./videos
```

#### Option D: Custom Question
```bash
python test_video_model.py --video sample.mp4 --question "What activities are happening in this video?"
```

## Example Questions

You can ask various questions about your videos:

1. "Describe what you see in this video."
2. "What activities are happening in this video?"
3. "What objects can you identify in this video?"
4. "Describe the setting and environment."
5. "What is the main action or event in this video?"
6. "How many people are in this video?"
7. "What is the mood or atmosphere of this video?"

## Command Line Options

```bash
python test_video_model.py [OPTIONS]

Options:
  --model_path PATH     Path to model directory (default: current directory)
  --video PATH          Path to single video file to test
  --video_dir PATH      Directory containing video files to test
  --question TEXT       Question to ask about the video
  --device DEVICE       Device to use: auto, cpu, cuda (default: auto)
  --help               Show help message
```

## Requirements

- Python 3.8+
- PyTorch 2.0+
- Transformers 4.35+
- OpenCV
- Other dependencies listed in requirements.txt

## GPU Support

The script automatically detects and uses GPU if available. For best performance:
- NVIDIA GPU with CUDA support
- At least 8GB VRAM for the 8B model

If no GPU is available, the model will run on CPU (slower but functional).

## Model Information

- **Base Model**: unsloth/meta-llama-3.1-8b-bnb-4bit
- **Quantization**: 4-bit quantization for memory efficiency
- **Framework**: Hugging Face Transformers with Unsloth optimizations

## Troubleshooting

### Common Issues

1. **Out of Memory Error**
   - Reduce video resolution or number of frames
   - Use CPU instead of GPU: `--device cpu`
   - Close other applications using GPU memory

2. **Model Loading Error**
   - Check internet connection (may need to download from Hugging Face)
   - Verify model files are present
   - Try running setup.py again

3. **Video Loading Error**
   - Check video file format is supported
   - Verify file path is correct
   - Try with a different video file

### Performance Tips

- Use GPU for faster inference
- Smaller videos process faster
- Batch processing multiple videos is more efficient

## File Structure

```
llama3.1-videoclip-test-v13/
├── test_video_model.py      # Main testing script
├── example_usage.py         # Example usage demonstration
├── setup.py                 # Setup and installation script
├── requirements.txt         # Python dependencies
├── TESTING_README.md        # This file
├── videos/                  # Place your video files here
└── outputs/                 # Generated outputs (if any)
```

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Verify all dependencies are installed correctly
3. Test with a simple, short video first
4. Check GPU memory usage if using CUDA

## License

This testing framework follows the same license as the base model (Apache 2.0).
