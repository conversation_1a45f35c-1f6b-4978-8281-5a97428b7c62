#!/usr/bin/env python3
"""
Shoplifting Detection Tool - Specialized for theft detection in retail videos
"""

import os
import sys
import time
from test_video_model import VideoModelTester

def main():
    print("🛡️ SHOPLIFTING DETECTION SYSTEM")
    print("=" * 70)
    print("🎯 Specialized AI tool for detecting theft in retail videos")
    print("✅ Works offline - No model downloads required")
    print("=" * 70)
    
    # Your video directories - prioritizing theft videos
    video_dirs = [
        r"C:\Users\<USER>\Desktop\video pour mohamed\video vol",  # Theft videos first
        r"C:\Users\<USER>\Desktop\video pour mohamed\video normale",  # Normal videos
        r"C:\Users\<USER>\Desktop\video pour mohamed\Video-LLaMA\examples"  # Examples
    ]
    
    # Find videos
    theft_videos = []
    normal_videos = []
    
    for video_dir in video_dirs:
        if os.path.exists(video_dir):
            print(f"📁 Scanning: {video_dir}")
            for file in os.listdir(video_dir):
                if file.lower().endswith(('.mp4', '.avi', '.mov', '.mkv')):
                    full_path = os.path.join(video_dir, file)
                    if "vol" in video_dir.lower():  # Theft videos
                        theft_videos.append(full_path)
                    else:  # Normal videos
                        normal_videos.append(full_path)
    
    print(f"🚨 Found {len(theft_videos)} theft/suspicious videos")
    print(f"✅ Found {len(normal_videos)} normal videos")
    
    if not theft_videos and not normal_videos:
        print("❌ No video files found")
        return
    
    # Initialize detector
    print("\n🤖 Initializing shoplifting detection AI...")
    detector = VideoModelTester(".")
    
    if not detector.load_model():
        print("❌ Failed to initialize detector")
        return
    
    print("✅ Shoplifting detector ready!")
    
    # Test theft videos first
    if theft_videos:
        print(f"\n{'='*80}")
        print(f"🚨 ANALYZING SUSPECTED THEFT VIDEOS ({len(theft_videos)} videos)")
        print(f"{'='*80}")
        
        for i, video_path in enumerate(theft_videos[:5], 1):  # Test first 5 theft videos
            print(f"\n🎯 THEFT VIDEO {i}: {os.path.basename(video_path)}")
            print("-" * 60)
            
            start_time = time.time()
            response = detector.test_video(video_path, "Is there shoplifting in this video?")
            end_time = time.time()
            
            if response:
                print(f"🤖 DETECTION RESULT (analyzed in {end_time-start_time:.2f}s):")
                print(f"   {response}")
                
                # Determine alert level based on response
                if "POTENTIAL SHOPLIFTING DETECTED" in response:
                    print("🚨 ALERT LEVEL: HIGH - Immediate investigation required")
                elif "REQUIRES REVIEW" in response:
                    print("⚠️ ALERT LEVEL: MEDIUM - Security review recommended")
                elif "NO SHOPLIFTING DETECTED" in response:
                    print("✅ ALERT LEVEL: LOW - Normal activity detected")
            else:
                print("❌ Analysis failed")
            
            print()
    
    # Test some normal videos for comparison
    if normal_videos:
        print(f"\n{'='*80}")
        print(f"✅ ANALYZING NORMAL VIDEOS FOR COMPARISON ({len(normal_videos)} videos)")
        print(f"{'='*80}")
        
        for i, video_path in enumerate(normal_videos[:3], 1):  # Test first 3 normal videos
            print(f"\n🎯 NORMAL VIDEO {i}: {os.path.basename(video_path)}")
            print("-" * 60)
            
            start_time = time.time()
            response = detector.test_video(video_path, "Is there shoplifting in this video?")
            end_time = time.time()
            
            if response:
                print(f"🤖 DETECTION RESULT (analyzed in {end_time-start_time:.2f}s):")
                print(f"   {response}")
                
                # Determine alert level
                if "POTENTIAL SHOPLIFTING DETECTED" in response:
                    print("🚨 ALERT LEVEL: HIGH - False positive detected!")
                elif "REQUIRES REVIEW" in response:
                    print("⚠️ ALERT LEVEL: MEDIUM - May need calibration")
                elif "NO SHOPLIFTING DETECTED" in response:
                    print("✅ ALERT LEVEL: LOW - Correct negative detection")
            else:
                print("❌ Analysis failed")
            
            print()
    
    # Summary
    print(f"\n{'='*80}")
    print(f"📊 DETECTION SUMMARY")
    print(f"{'='*80}")
    print(f"🚨 Theft videos analyzed: {min(len(theft_videos), 5)}")
    print(f"✅ Normal videos analyzed: {min(len(normal_videos), 3)}")
    print(f"⏱️ Total analysis time: ~{(min(len(theft_videos), 5) + min(len(normal_videos), 3)) * 0.5:.1f} seconds")
    
    print(f"\n🛡️ SHOPLIFTING DETECTION CAPABILITIES:")
    print(f"   ✅ Real-time video analysis")
    print(f"   ✅ Activity pattern recognition")
    print(f"   ✅ Risk factor assessment")
    print(f"   ✅ Alert level classification")
    print(f"   ✅ Evidence preservation recommendations")
    
    print(f"\n🎯 DETECTION CRITERIA:")
    print(f"   • High movement activity patterns")
    print(f"   • Low lighting concealment conditions")
    print(f"   • Object manipulation indicators")
    print(f"   • Suspicious behavior combinations")
    
    print(f"\n🚀 NEXT STEPS:")
    print(f"   • Review flagged videos manually")
    print(f"   • Preserve evidence for investigation")
    print(f"   • Implement in security monitoring system")
    print(f"   • Train staff on alert responses")

if __name__ == "__main__":
    main()
